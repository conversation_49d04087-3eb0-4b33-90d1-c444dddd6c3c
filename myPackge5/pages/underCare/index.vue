<template>
    <view class="page-container">
        <CustomNavbar :bgColor="'#08BA7E'" :titleColor="'#FFFFFF'" />
        <view class="header">
            <view class="fifter" v-if="currentTab!=1">
                <img src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/xiaoshouhetong/shaixuan.png"
                    alt="" @click="fifterClick" />
            </view>
        </view>

        <view class="tab-container">
            <scroll-view class="tabs-scroll" scroll-x="true" show-scrollbar="false" scroll-with-animation="true"
                :scroll-into-view="shouldScroll ? scrollIntoView : ''">
                <view class="tabs">
                    <view class="tab-item" :class="{ 'active': currentTab === 1 }" @click="switchTab(1)" :id="'tab-1'">
                        <view class="icon-box" :class="{ 'active': currentTab === 1 }">
                            <image :src="`${obs}/nmb-mini/mine/kucunguanli.png`" class="tab-icon" />
                        </view>
                        库存统计
                        <view v-if="currentTab === 1" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 2 }" @click="switchTab(2)" :id="'tab-2'">
                        <view class="icon-box" :class="{ 'active': currentTab === 2 }">
                            <image :src="`${obs}/nmb-mini/mine/siyang.png`" class="tab-icon" />
                        </view>
                        饲养管理
                        <view v-if="currentTab === 2" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 3 }" @click="switchTab(3)" :id="'tab-3'">
                        <view class="icon-box" :class="{ 'active': currentTab === 3 }">
                            <image :src="`${obs}/nmb-mini/mine/shengzhang.png`" class="tab-icon" />
                        </view>
                        生长监测
                        <view v-if="currentTab === 3" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 4 }" @click="switchTab(4)" :id="'tab-4'">
                        <view class="icon-box" :class="{ 'active': currentTab === 4 }">
                            <image :src="`${obs}/nmb-mini/mine/jeizhong.png`" class="tab-icon" />
                        </view>
                        接种记录
                        <view v-if="currentTab === 4" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 5 }" @click="switchTab(5)" :id="'tab-5'">
                        <view class="icon-box" :class="{ 'active': currentTab === 5 }">
                            <image :src="`${obs}/nmb-mini/mine/jibing.png`" class="tab-icon" />
                        </view>
                        疾病治疗
                        <view v-if="currentTab === 5" class="bubble-arrow"></view>
                    </view>
                    <view class="tab-item" :class="{ 'active': currentTab === 6 }" @click="switchTab(6)" :id="'tab-6'">
                        <view class="icon-box" :class="{ 'active': currentTab === 6 }">
                            <image :src="`${obs}/nmb-mini/mine/richang.png`" class="tab-icon" />
                        </view>
                        日常记录
                        <view v-if="currentTab === 6" class="bubble-arrow"></view>
                    </view>
                </view>
            </scroll-view>
        </view>

        <view class="tab-content">
            <InventoryStatistics v-if="currentTab === 1" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <FeedingManagement v-if="currentTab === 2" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <GrowthMonitoring v-if="currentTab === 3" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
            <VaccinationRecord v-if="currentTab === 4" :filterParams="filterParams" :resetSearch="resetSearchFlag" @showVaccinationDetail="showVaccinationDetail" />
            <DiseaseControl v-if="currentTab === 5" :filterParams="filterParams" :resetSearch="resetSearchFlag" @showDiseaseDetail="showDiseaseDetail" />
            <DailyRecord v-if="currentTab === 6" :filterParams="filterParams" :resetSearch="resetSearchFlag" />
        </view>
        <filterPopup @resetSearch="resetSearch" :filterType="filterType" :pickerFilterShow="pickerFilterShow"
            @canel="pickerFilterShow = false" @submitForm="submitForm" />

        <VaccinationDetailModal
          :visible="showVaccinationDetailModal"
          :detailData="vaccinationDetailData"
          @close="closeVaccinationDetailModal"
          :showType="showType"
        />

        <VaccinationDetailModal
          :visible="showDiseaseDetailModal"
          :detailData="diseaseDetailData",
          :showType="showType"
          @close="closeDiseaseDetailModal"
        />
    </view>
</template>

<script>
import filterPopup from './components/filterPopup.vue'
import CustomNavbar from '@/components/uni-custom-navbar/CustomNavbar.vue'
import InventoryStatistics from './components/InventoryStatistics.vue'
import FeedingManagement from './components/FeedingManagement.vue'
import GrowthMonitoring from './components/GrowthMonitoring.vue'
import VaccinationRecord from './components/VaccinationRecord.vue'
import DiseaseControl from './components/DiseaseControl.vue'
import DailyRecord from './components/DailyRecord.vue'
import VaccinationDetailModal from './components/VaccinationDetailModal.vue'
const app = getApp();

export default {
    components: {
        CustomNavbar,
        filterPopup,
        InventoryStatistics,
        FeedingManagement,
        GrowthMonitoring,
        VaccinationRecord,
        DiseaseControl,
        DailyRecord,
        VaccinationDetailModal
    },
    data() {
        return {
            obs: app.globalData.obs,
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            pickerFilterShow: false,
            filters: {},
            list: [],
            currentTab: 1, // 当前选中的tab
            scrollIntoView: '', // 滚动到指定元素
            shouldScroll: false, // 是否需要滚动的标志
            totalTabs: 6, // 总tab数量
            visibleTabCount: 3, // 可视区域完全显示的tab数量
            currentVisibleStart: 1, // 当前可视区域起始位置
            filterType: 'inventory',
            filterParams: {}, // 筛选参数
            resetSearchFlag: false, // 重置搜索标志

            // 疫苗详情弹窗相关
            showVaccinationDetailModal: false,
            vaccinationDetailData: null,

            // 疾病防控详情弹窗相关
            showDiseaseDetailModal: false,
            diseaseDetailData: null,
            showType: ''
        }
    },
    onLoad() {

    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {
        switchTab(tabIndex) {
            // 如果点击的是当前已激活的tab，直接返回
            if (tabIndex === this.currentTab) {
                console.log('点击当前已激活的tab，无需处理');
                return;
            }

            const typeMap = { 1: 'inventory', 2: 'breeding', 3: 'growth', 4: 'vaccination', 5: 'disease', 6: 'daily' };

            // 切换tab时清空筛选条件
            this.filterParams = {};
            this.resetSearchFlag = !this.resetSearchFlag;
            this.currentTab = tabIndex;
            this.filterType = typeMap[tabIndex];

            this.$nextTick(() => {
                const targetScrollTab = this.calculateTargetScrollTab(tabIndex);
                if (targetScrollTab !== null) {
                    // 需要滚动
                    console.log(`需要滚动到tab-${targetScrollTab}`);
                    this.shouldScroll = true;
                    this.scrollIntoView = `tab-${targetScrollTab}`;
                    // 更新当前可视区域起始位置
                    this.updateCurrentVisibleStart(targetScrollTab);

                    setTimeout(() => {
                        this.shouldScroll = false;
                        this.scrollIntoView = '';
                    }, 300);
                } else {
                    // 不需要滚动
                    console.log('不需要滚动，保持当前状态');
                    this.shouldScroll = false;
                    this.scrollIntoView = '';
                }
            });
        },



        calculateTargetScrollTab(targetTab) {
            if (this.totalTabs <= this.visibleTabCount) {
                console.log('总tab数不超过可视数量，无需滚动');
                return null;
            }

            // 获取当前可视区域
            const currentVisibleStart = this.getCurrentVisibleStart();
            const currentVisibleEnd = currentVisibleStart + this.visibleTabCount - 1;


            let scrollToTab = null;

            const isInVisibleArea = targetTab >= currentVisibleStart && targetTab <= currentVisibleEnd;
            const isOutsideLeft = targetTab < currentVisibleStart;
            const isOutsideRight = targetTab > currentVisibleEnd;

            if (isInVisibleArea) {
                const isAdjacentToActive = Math.abs(targetTab - this.currentTab) === 1;

                if (isAdjacentToActive) {
                    // 点击相邻tab，不滚动
                    console.log('点击相邻tab，不滚动');
                    return null;
                } else {
                    // 点击间隔tab，需要滚动
                    if (targetTab > this.currentTab) {
                        // 向右滑动：让目标tab成为可视区域第二个
                        scrollToTab = targetTab - this.visibleTabCount + 2;
                    } else {
                        // 向左滑动：让目标tab成为可视区域第二个
                        scrollToTab = targetTab - 1;
                    }
                }
            } else if (isOutsideLeft) {
                // 点击左侧外部tab，向右滑动，让目标tab成为第二个
                scrollToTab = targetTab - 1;
            } else if (isOutsideRight) {
                scrollToTab = targetTab - this.visibleTabCount + 2;
            }

            // 确保滚动目标在有效范围内
            if (scrollToTab !== null) {
                scrollToTab = Math.max(1, Math.min(scrollToTab, this.totalTabs - this.visibleTabCount + 1));
            } else {
            }

            return scrollToTab;
        },

        getCurrentVisibleStart() {
            if (!this.currentVisibleStart) {
                this.currentVisibleStart = 1; // 初始状态从第一个tab开始
            }
            return this.currentVisibleStart;
        },

        // 更新当前可视区域起始位置
        updateCurrentVisibleStart(newStart) {
            this.currentVisibleStart = newStart;
        },

        // 动态设置tab数量 
        setTotalTabs(count) {
            this.totalTabs = count;
            // 重置可视区域到开始位置
            this.currentVisibleStart = 1;
        },

        // 动态设置可视tab数量 
        setVisibleTabCount(count) {
            this.visibleTabCount = count;
            // 重新计算当前可视区域
            this.currentVisibleStart = Math.min(this.currentVisibleStart, this.totalTabs - count + 1);
        },
        // 搜索
        fifterClick() {
            this.pickerFilterShow = true
        },

        resetSearch() {
            this.filterParams = {};
            this.resetSearchFlag = !this.resetSearchFlag;
        },
        submitForm(val) {
            this.filterParams = { ...val };
            this.pickerFilterShow = false;
        },

        // 疫苗详情弹窗相关方法
        showVaccinationDetail(data) {
            this.vaccinationDetailData = data;
            this.showVaccinationDetailModal = true;
            this.showType = 'showVaccinationDetail'
        },

        closeVaccinationDetailModal() {
            this.showVaccinationDetailModal = false;
            this.vaccinationDetailData = null;
        },

        // 疾病防控详情弹窗相关方法
        showDiseaseDetail(data) {
            this.diseaseDetailData = data;
            this.showDiseaseDetailModal = true;
            this.showType = 'showDiseaseDetail'

        },

        closeDiseaseDetailModal() {
            this.showDiseaseDetailModal = false;
            this.diseaseDetailData = null;
        },
    },
}
</script>

<style lang="scss">
page {
  height: 100vh;
}
</style>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.header {
    width: 750rpx;
    height: 727rpx;
    display: flex;
    padding-top: 120rpx;
    /* 导航栏空间 */
    box-sizing: border-box;
    position: relative;
    background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/bg.png) no-repeat 100% 100%;
    background-size: 100% 100%;
    position: relative;
}

.fifter {
    position: absolute;
    top: 195rpx;
    right: 30rpx;

    img {
        width: 34rpx;
        height: 32.5rpx;
    }
}

.tab-container {
    margin-top: -372rpx;
    padding: 0 26rpx;
    // margin-bottom: 30rpx;
    overflow: visible;
}

.tabs-scroll {
    width: 100%;
    white-space: nowrap;
    overflow: visible;
    padding-bottom: 15rpx;
}

.tabs {
    display: flex;
    align-items: center;
    width: max-content;
    padding: 0 10rpx;
    overflow: visible;
    height: 100rpx;
}

.tab-item {
    width: 188rpx;
    height: 65rpx;
    background: #FFFFFF;
    border-radius: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1DB17A;
    font-weight: 500;
    font-size: 26rpx;
    position: relative;
    transition: all 0.3s ease;
    margin-right: 17rpx;
    flex-shrink: 0;
    padding: 5rpx 20rpx 5rpx 5rpx;

    .icon-box {
        width: 50rpx;
        height: 50rpx;
        background-color: #E3FFEE;
        border-radius: 50%;
        margin-right: 10rpx;
        padding: 12rpx;
        box-sizing: border-box;

        &.active {
            background-color: #ffffff !important;
        }

        .tab-icon {
            width: 25rpx;
            height: 25rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    &:last-child {
        margin-right: 10rpx;
    }

    &.active {
        color: #FFFFFF;
        background: linear-gradient(140deg, #1CC271 0%, #5CD26F 100%);
    }
}

.bubble-arrow {
    position: absolute;
    bottom: -10rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 10rpx solid transparent;
    border-right: 10rpx solid transparent;
    border-top: 10rpx solid #5CD26F;
    z-index: 999;
}

.tab-content {
    padding: 0 30rpx;
    height: calc(100vh - 500rpx);
}

.main {
    margin-top: -372rpx;
}

.Add {
    width: 152rpx;
    height: 145rpx;
    position: absolute;
    bottom: 290rpx;
    right: 10rpx;

    img {
        width: 152rpx;
        height: 145rpx;
    }
}
</style>
