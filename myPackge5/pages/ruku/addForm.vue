<template>
    <view>
        <CustomNavbar :title="`新建入库`" :titleColor="`#333`" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
             <view class="container">
				<view class="cultivate-section">
				    <view class="section-title">入库类型：</view>
				    <view class="cultivate-options">
				        <view v-for="item in cultivateList" :key="item.value"
				            :class="['cultivate-item', { 'active': form.inventoryType === item.value }]"
				            @click="!isDetail && selectCultivateType(item.value)">
				            {{ item.label }}
				        </view>
				    </view>
				</view>
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
					
					<template v-if="form.inventoryType == 1">
					   <u-form-item label="订单编号：" required 
					       :right-icon="isDetail ? '' : 'arrow-right'">
					       <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
					           :value="`${CurrOrder == null?'':CurrOrder.purchaseOrderCode}`" placeholder="请选择订单编号" disabled
					           @click="!isDetail && (showOrder = true)" />
					   </u-form-item>
					   <u-form-item label="供应商" required >
					       <u-input v-model="form.saleOrderSupplierName" maxlength="10" placeholder="请输入供应商"
					           :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail" />
					   </u-form-item>
					   <u-form-item label="批次号" required >
					       <u-input v-model="form.saleOrderBatchCode" maxlength="18" placeholder="请输入批次号"
					           :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="true" />
					   </u-form-item>
					</template>
					
					<view class="container" style="padding: 0px; margin: 25rpx 0;">
						<view class="cultivate-section">
						    <view class="section-title">入库模式：</view>
						    <view class="cultivate-options">
						        <view v-for="item in rukuMode" :key="item.value"
						            :class="['cultivate-item', { 'active': form.earTagType == item.value }]"
						            @click="form.earTagType = item.value ; form.recordList[0].num = 0">
						            {{ item.label }}
						        </view>
						    </view>
						</view>
					</view>
					<template v-if="form.earTagType == 0">
						<u-form-item label="活畜类别" required prop="pastureName" :right-icon="'arrow-right'">
							<u-input v-model="liveType" placeholder="请选择活畜类别"
									:custom-style="customStyle" @click="typeShow = true" :placeholder-style="placeholderStyle" :disabled="true" />
						</u-form-item>
						
						<u-form-item label="活畜数量（头）" required prop="num">
							<u-input v-model="form.recordList[0].num" maxlength="10" placeholder="请输入（不能超过订单数量）"
								:custom-style="customStyle" :placeholder-style="placeholderStyle" @blur='changeNumber' :disabled="isDetail" />
						</u-form-item>
					</template>
					<template v-if="form.earTagType == 1">
						<u-form-item label="请选择活畜" required prop="pastureName" :right-icon="'arrow-right'">
							<u-input v-model="form.earList.join(',')" placeholder="请选择活畜"
									:custom-style="customStyle" @click="chooseLiveAnimal" :placeholder-style="placeholderStyle" :disabled="true" />
						</u-form-item>
						
					</template>
                    <u-form-item label="疫苗接种情况" required prop="pastureNature"
                        :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getNatureText(form.pastureNature)" placeholder="请选择疫苗接种情况" disabled
                            @click="!isDetail && (showNatureSelect = true)" />
                    </u-form-item>
                    <u-form-item label="疾病史"  prop="diseaseHistory" >
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="form.diseaseHistory"
                            placeholder="请输入疾病史"  />
                    </u-form-item>
                    <u-form-item label="检疫结果" prop="quarantineResult" v-if="getNatureText(form.pastureNature) =='已接种'">
                        <u-input v-model="form.quarantineResult" placeholder="请输入检疫结果" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" :disabled="isDetail" />
                    </u-form-item>
					
					<u-form-item label="入库日期" required prop="stockInDate"  :right-icon="'arrow-right'">
					    <u-input v-model="form.stockInDate" placeholder="请选择入库日期" :custom-style="customStyle"
					        :placeholder-style="placeholderStyle" :disabled="true"  @click="showData = true" />
					</u-form-item>

                    <!-- 养殖场 -->
                    <u-form-item label="养殖场" required prop="pastureId" :right-icon="isDetail ? '' : 'arrow-right'">
                        <u-input v-model="form.pastureName" placeholder="请选择请选择" type="number"
                            :custom-style="customStyle"  @click="pastureSelectFn" :placeholder-style="placeholderStyle" :disabled="true" />
                    </u-form-item>
					<u-form-item label="圈舍" required prop="penName" :right-icon="isDetail ? '' : 'arrow-right'">
					    <u-input v-model="form.penName" placeholder="请选择圈舍" type="number"
					        :custom-style="customStyle"  @click="penSelectFn" :placeholder-style="placeholderStyle" :disabled="true" />
					</u-form-item>
                </u-form>
                
            </view>
            <!-- <view class="upload-section">
                <view class="section-title">
                    养殖场照片：
                    <img v-if="!isDetail" class="upload-icon" src="../../icon/photo.png"
                        @click="uploadImage('farmImages', 3)"></img>
                </view>
                <view class="section-subtitle">最多不超过3张，单张图片小于2M</view>

                <view class="uploadImage" v-if="form.farmImages.length">
                    <view class="itemAlready" v-for="(item, index) in form.farmImages" :key="index">
                        <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
                        <view v-if="!isDetail" class="closeIcon" @click="deleteImage(index, 'farmImages')"></view>
                    </view>
                </view>
            </view> -->
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">确认</view>
        </view>

        <address-picker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow"
            @addressCanel="pickerAreaShow = false" :titleShow="false" :showLevel3="true" />

        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showNatureSelect" mode="single-column"
            :list="yimaioMode" label-name="label" @confirm="selectNature" value-name="value" />
			
		
		<u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="pastureSelect" mode="single-column"
		    :list="pastureList" label-name="pastureName" @confirm="selectPasture" value-name="pastureId" />
			
		<u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="penSelect" mode="single-column"
		    :list="penList" label-name="penName" @confirm="selectPen" value-name="penId" />
			
		<u-select v-model="typeShow" confirm-color='#40CA8F' mode="mutil-column" :list="typeList" @confirm="confirmType"></u-select>
			
	    <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F"  range-color="#40CA8F" btn-type="success" v-model="showData" :mode="`date`" @change='changeData'  :max-date='maxdata' :min-date="mindata"></u-calendar>
	    
		<u-popup v-model="showOrder" mode='bottom' height ='85%' :closeable="true">
		   <orderList :listData='orderList' @UpdateListData='UpdateListData' @ChooseOrder='ChooseOrder'></orderList>
		</u-popup>
		
		<u-popup v-model="liveAnimalShow" mode='bottom' height ='85%' :closeable="true"> 
		   <eartag :eartagItem='eartagItem' @UpdateEartagItem='UpdateEartagItem' @ChooseEarTag='ChooseEarTag'></eartag>
		</u-popup>
		
    </view>
</template>

<script>
import addressPicker from '@/components/address-picker/index.vue'
import { pastureAdd, pastureEdit, pastureDetail } from '@/api/pages/livestock/farm'
import {
    getDicts,
    livestockCategory,
    animalTypeList,
	getOrderBatchno
} from "@/api/dict.js"

import { uploadFiles } from '@/api/obsUpload/index'
import CustomNavbar from '../components/CustomNavbar.vue'
import { pasturePage,penList } from '@/api/pages/livestock/farm'
import {
	 myTradingList
} from '@/api/account.js'
import orderList from '../components/orderList.vue'
import eartag from '../components/eartag/index.vue'
import { purchaseOrderPage,orderEarTag,purchaseOrderIn } from '@/api/pages/purchaseOrder' 
export default {
    name: 'addFarm',
    components: {
        addressPicker,
        CustomNavbar,
		orderList,
		eartag
    },

    data() {
        return {
			mindata: "2024-01-01",
			maxdata:'2095-09-09',
            systemInfo: uni.getSystemInfoSync(),
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            // 页面状态
            pastureId: '', // 养殖场ID
			pastureList:[],
			penList:[],
			typeList:[],
			showOrder:false,
            //isEdit: false, // 是否编辑模式
            isDetail: false, // 是否详情模式
			showData:false,
			typeShow:false,
			liveAnimalShow:false, //显示活畜耳标
            form: {
				saleOrderSupplierName:"",//供应商名称
				saleOrderBatchCode:"" ,//批次
				purchaseOrderCode:'',
				activeType:1,
                pastureName: '',
				penName:'',
				earTagType:0,
                quarantineResult: '',
                pastureNature: '',
                inventoryType: '1',
                diseaseHistory: '',              
				stockInDate:'',
				recordList:[
					{
						num:'',
						typeId:"403292860613267456",
						typeName:"牛",
						varietiesId:"",
						varietiesName:"",
						categoryId:"",
						categoryName:""
					}
				],
				earList:[],
				pastureId:"",
				penId:""
            },
            pickerAreaShow: false,
            showNatureSelect: false,
			pastureSelect:false,
			penSelect:false,
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            isSubmitting: false,
            natureList: [],
            cultivateList: [{
				value:"1",
				label:"外购"
			},{
				value:"2",
				label:"自繁"
			}],
			rukuModeVal: "0",
			rukuMode:[
				{
					value:"0",
					label:"无耳标入库"
				},{
					value:"1",
					label:"耳标入库"
				}
			],
			yimaioMode:[
				{
					value:"1",
					label:"已接种"
				},{
					value:"2",
					label:"未接种"
				}
			],
			orderList:[],
			CurrOrder:null,
			eartagItem: null,
            chooseEarTagList:[] //已经选择的耳标编号
        }
    },

    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 44;
            return statusBarHeight + navbarHeight;
        },
        
        isFormValid() {
            const { pastureName, provinceName, address, pastureNature, cultivateType, farmImages } = this.form
            return pastureName && provinceName && address && pastureNature !== '' && cultivateType !== '' && farmImages.length > 0
        },
		liveType(){
			if(!(this.form.recordList[0].varietiesName || this.form.recordList[0].varietiesName)) return "";
			return `${this.form.recordList[0].varietiesName } - ${this.form.recordList[0].categoryName}` 
		},
    },

    onLoad(options) {
        // 判断页面模式
        if (options.pastureId) {
            this.pastureId = options.pastureId;
            //this.isEdit = options.mode === 'edit';
            this.isDetail = options.mode === 'detail';
        }
        //初始化数据
        this.initData();
       // this.loadNatureDict();
       // this.loadCultivateDict();

        // 编辑或详情
       /* if (this.pastureId) {
            this.loadFarmDetail();
        } */
    },

   /* onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    }, */

    methods: {
		
		pastureSelectFn(){
			//如果牧场列表为空则提示
			if(this.pastureList.length == 0){
				this.$toast('缺少牧场信息，请先添加牧场')
				return
			}
			this.pastureSelect = true
		},
		penSelectFn(){
			//如果牧场列表为空则提示
			if(this.penList.length == 0){
				this.$toast('缺少圈舍信息，请先添加圈舍')
				return
			}
			this.penSelect = true
		},
		/**
		 * 初始化数据
		 */
		async initData() {			 
		     const pasture = await pasturePage({ pageNum: 1, pageSize: 1000 });		   
			 if(pasture.code == 200){
				 this.pastureList = pasture.result.list;
				 console.log(this.pastureList)
			 }
			 livestockCategory({
				pageNum: 1,
				pageSize: 100000,
				categoryType: '403292860613267456'
			}).then(res => {				
				this.typeList[0] = res.result.map(item =>{
					 return {
						 value : item.varietiesId,
						 label : item.varietiesName
					 }
				}) || [];
			})
			animalTypeList({
				pageSize: 9999,
				pageNum: 1,
				categoryType: '403292860613267456'
			}).then(res => {
				this.typeList[1] = res.result.map(item =>{
					 return {
						 value : item.categoryId,
						 label : item.categoryName
					 }
				}) || [];
			});
			
			
			purchaseOrderPage({
				pageNum: 1,
				pageSize: 1000,
				showStatus: 42 //已验收
			}).then(r => {
				this.orderList = r.result.list.map(item =>{
					 return {
						 ...item,
						 checked:false
					 }
				});
				
			})
			 
		},
		UpdateListData(data){
			this.orderList = data;
		},
		UpdateEartagItem(data){
			this.eartagItem = data 
		},
		ChooseOrder(item){
			
			console.log(item);
			this.showOrder = false;
			if(item == null) return;
			this.CurrOrder = item;
			this.form.recordList[0].num = this.form.earTagType == 0 ?   item.livestockNum : '';
			this.form.purchaseOrderCode = item.purchaseOrderCode;	
			
			getOrderBatchno({
				serviceField:item.purchaseOrderCode
			}).then(r=>{
				 this.form.saleOrderBatchCode = r.result;
			})			
		},
		ChooseEarTag(items){
			console.log(items);
			this.chooseEarTagList = items
			this.liveAnimalShow = false;
			console.log(this.chooseEarTagList);
			this.form.earList = this.chooseEarTagList.map(item => {
				 return item.eartag
			});
			console.log(this.form.earList)
		},
		changeData(e){
			this.form.stockInDate = e.result;
			this.showData = false;
		},
		changeNumber(e){			
			if(this.CurrOrder != null && e > this.CurrOrder.livestockNum && this.form.earTagType == 0 ) {
				 this.form.recordList[0].num = this.CurrOrder.livestockNum;
				 this.$toast('入库数量不能大于采购单数量')
				 return;
			}
		},
		/**
		 * 选择耳标
		 */
		chooseLiveAnimal(){
			const that = this;
			if(that.CurrOrder == null && that.form.inventoryType == 1){
				 return  this.$toast('请先选择订单')
			}			
			
			orderEarTag(that.CurrOrder.purchaseOrderId).then(r=>{
				 if(r.result.earTags.length == 0) return this.$toast('该订单没有带耳标的牛只');				 
			 	 r.result.earTags =   r.result.earTags.map(item =>{
					  return  {
						  eartag:item,
						  checked:that.form.earList.filter(ear => ear == item).length > 0   
					  }
				 });
				that.eartagItem = r.result;				
				that.liveAnimalShow = true;
			});
			//获取订单耳标
		},
        /* async loadNatureDict() {
            try {
                const res = await getDicts('pasture_nature');
                if (res && res.data) {
                    this.natureList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载字典失败:', error);
                this.$toast('加载字典失败');
            }
        }, */

       /* async loadCultivateDict() {
            try {
                const res = await getDicts('cultivate_type');
                if (res && res.data) {
                    this.cultivateList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载养殖方式字典失败:', error);
            }
        }, */

        // 加载养殖场详情
       /* async loadFarmDetail() {
            try {
                const res = await pastureDetail({ pastureId: this.pastureId });
                if (res.code === 200 && res.result) {
                    const data = res.result;
                    this.form = {
                        pastureName: data.pastureName || '',
                        address: data.address || '',
                        breedingQuantity: data.breedingQuantity || '',
                        pastureNature: data.pastureNature || '',
                        cultivateType: data.cultivateType || '',
                        urls: data.farmImages ? data.farmImages.split(',').filter(img => img) : [],
                        provinceName: data.provinceName || '',
                        provinceId: data.provinceId || '',
                        cityName: data.cityName || '',
                        cityId: data.cityId || '',
                        countyName: data.countyName || '',
                        countyId: data.countyId || ''
                    };
                } else {
                    throw new Error(res.message || '获取详情失败');
                }
            } catch (error) {
                console.error('加载养殖场详情失败:', error);
                this.$toast('加载详情失败');
                uni.navigateBack();
            }
        }, */

        getNatureText(value) {
            const nature = this.yimaioMode.find(item => item.value === value)
            return nature ? nature.label : ''
        },

        submitAddress(val) {
            if (!val?.areaName || !val?.areaValue) return

            const areaName = val.areaName.split('-')
            const areaId = val.areaValue.split(',')

            this.form = {
                ...this.form,
                provinceName: areaName[0] || '',
                provinceId: areaId[0] || '',
                cityName: areaName[1] || '',
                cityId: areaId[1] || '',
                countyName: areaName[2] || '',
                countyId: areaId[2] || ''
            }

            this.pickerAreaShow = false
            this.resetField('provinceName')
        },

        // 选择疫苗接种情况
        selectNature(value) {
            if (!value?.length) return
            this.form.pastureNature = value[0].value
            this.showNatureSelect = false
            this.resetField('pastureNature')
        },
		/**
		 * 选择养殖场
		 */
		selectPasture(value){
			
			if (!value?.length) return
			const item = value[0];
			console.log(item)
			const pastureItem = this.pastureList.find(i => i.pastureId == item.value);
			console.log(pastureItem)
			this.form.pastureName = pastureItem.pastureName; 
			this.form.pastureId = item.value;
			this.getPenList(pastureItem.pastureId);
			this.pastureSelect = false;
			this.resetField('pastureName')
		},
		
		async getPenList(pastureId){
			 const that = this;
			 console.log(pastureId)
			 const res = await penList({pastureId});
			  if(res.code == 200){
				  that.penList = res.result;
			  }
		},
		selectPen(value){
			if (!value?.length) return
			const item = value[0];
			const penItem = this.penList.find(i => i.penId == item.value);
			this.form.penName = penItem.penName; 
			this.form.penId  = item.value;
			this.penSelect = false;
			this.resetField('penName')
		},
		confirmType(value){			
			this.form.recordList[0].varietiesId = value[0].value;
			this.form.recordList[0].varietiesName = value[0].label;
			this.form.recordList[0].categoryId = value[1].value;
			this.form.recordList[0].categoryName = value[1].label;
		},
        /**
		 * 选择入栏类型
		 * @param {Object} value 
		 */
        selectCultivateType(value) {
            this.form.inventoryType = value
            this.resetField('inventoryType')
        },

        // 上传图片
       /* uploadImage(type, maxCount) {
            if (this.form[type].length >= maxCount) {
                this.$toast(`图片最多只能上传${maxCount}张`)
                return
            }
            const that = this
            uni.chooseImage({
                count: maxCount - this.form[type].length,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: function (res) {
                    res.tempFilePaths.forEach(filePath => {
                        uploadFiles({
                            filePath: filePath,
                        }).then((data) => {
                            that.form[type].push(data)
                            that.resetField(type)
                        }).catch(error => {
                            console.error('上传失败:', error)
                            that.$toast('上传失败')
                        })
                    })
                },
                fail(e) {
                    console.error('选择图片失败:', e)
                },
            })
        },

        // 预览图片
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        }, 

        // 删除图片
        deleteImage(index, type) {
            this.form[type].splice(index, 1)
            this.resetField(type)
            this.$forceUpdate()
        },*/

        // 提交表单
        async submitForm() {
			if (this.isSubmitting) return;
			// 必填项校验
			const requiredFields = [
				{ key: 'inventoryType', label: '入库类型' },
				{ key: 'saleOrderSupplierName', label: '供应商' },
				{ key: 'saleOrderBatchCode', label: '批次号' },
				{ key: 'stockInDate', label: '入库日期' },
				{ key: 'pastureName', label: '养殖场' },
				{ key: 'penName', label: '圈舍' }
			];
			for (let i = 0; i < requiredFields.length; i++) {
				const { key, label } = requiredFields[i];
				if (!this.form[key] || this.form[key] === '' || this.form[key] === undefined) {
					this.$toast(`请填写${label}`);
					return;
				}
			}
			// 活畜类别和数量校验
			if (this.form.earTagType == 0) {
				if (!this.form.recordList[0].varietiesId || !this.form.recordList[0].categoryId) {
					this.$toast('请选择活畜类别');
					return;
				}
				if (!this.form.recordList[0].num || Number(this.form.recordList[0].num) <= 0) {
					this.$toast('请输入活畜数量');
					return;
				}
			} else if (this.form.earTagType == 1) {
				if (!this.form.earList || this.form.earList.length === 0) {
					this.$toast('请选择活畜');
					return;
				}
			}
			this.isSubmitting = true;
			try {
				// 防抖：防止重复提交
				if (this.form.earTagType == "1") {
					this.form.recordList[0].num = this.form.earList.length;
				}
				if (this.eartagItem) {
					this.form.recordList[0].categoryId = this.eartagItem.categoryId;
					this.form.recordList[0].varietiesId = this.eartagItem.varietiesId;
				}
				const res = await purchaseOrderIn(this.form);
				if (res && res.code === 200) {
					this.$toast('提交成功');
					setTimeout(() => {
						uni.navigateTo({ url: '/myPackge5/pages/ruku/index' });
					}, 800);
				} else {
					throw new Error(res && res.message ? res.message : '提交失败');
				}
			} catch (error) {
				this.handleError(error, '提交失败');
			} finally {
				this.isSubmitting = false;
			}
		},

        // validateForm() {
        //     return new Promise(resolve => {
        //         this.$refs.uForm.validate(valid => resolve(valid))
        //     })
        // },

        handleError(error, customMessage = '') {
            console.error(error)
            this.$toast(error.message || customMessage || '操作失败')
        },

        resetField(value) {
            if (!value) return
            this.$refs.uForm?.fields?.forEach(field => {
                if (field.prop === value) {
                    field.resetField()
                }
            })
        }
		
		
    },
}
</script>

<style lang="less" scoped>
	@import url('../../css/index.less');
</style>
