import config from '@/config/index'
import store from '@/store'

import {
	getToken
} from '@/common/utils/token.js'
import {
	getStorage
} from '@/common/utils/storage.js'
class Request {
	// 返回请求完整路径：域名+路径
	requestUrl(url) {
		if (url.startsWith('/')) {
			return (url = config.initEnv().BASE_URL + url)
		}
		return url
	}

	/**请求拦截
	 * @param {Boolean} checkLogin 请求路径
	 * @return Object  headers配置
	 */

	header(checkLogin) {
		const token = getToken()
		let headers = {}
		if (checkLogin) {
			if (token == '') {
				uni.showToast({
					title: '请先登录哦',
					duration: 3000,
					icon: 'none',
				})
				let pages = getCurrentPages();
				if (pages[0].route != 'pages/account/login/login') {
					console.log(9090)
					uni.redirectTo({
						url: '/pages/account/login/login',
					})
				}
				uni.hideLoading()
				return ''
			}
			headers['Authorization'] = token;
		}
		return headers
	}

	// 相应拦截
	statusCode(code, msg) {
		if (code == 401) {
			uni.showToast({
				title: msg || '请重新登录',
				icon: 'none',
			})
			store.commit('user/REMOVE_USER')
			uni.hideLoading()
			uni.showModal({
				title: '提示',
				content: '登录状态已过期，请去登录',
				showCancel: false,
				success: (err) => {
					let pages = getCurrentPages();
					if (pages[0].route != 'pages/account/login/login') {
						console.log(9090)
						uni.redirectTo({
							url: '/pages/account/login/login',
						})
					}
				},
			})
			return false
		}

		if (code != 200) {
			uni.showToast({
				title: msg || '服务端错误，联系管理员',
				icon: 'none',
				duration: 3000,
			})
			return false
		}
		return true
	}

	/**发送请求
	 * @param {String} url 请求路径
	 * @param {String} method 请求类型
	 * @param {Object} data 请求参数
	 * @param {Boolean} checkLogin  是否需要登录才能访问
	 * @return Object  请求结果
	 */
	ajax(url = '', data, method = 'GET', checkLogin = true) {
		let header = this.header(checkLogin)
		const tradingTenantId = getStorage('tenantId')
		return new Promise((resolve, reject) => {
			if (header == '') return reject(url + '请求被拦截')
			uni.request({
				header: header,
				url: this.requestUrl(url),
				method: method.toUpperCase(),
				data: {
					...data,
					// tradingTenantId
				},
				success: (res) => {
					let code = res.data.code || res.code
					let msg = res.data.message || res.data.msg || res.msg
					this.statusCode(code, msg)
					resolve(res)
				},
				fail: (err) => {
					uni.showToast({
						title: '服务端错误，联系管理员!!!',
						icon: 'none',
					})
					reject(err)
				},
			})
		})
	}
	ajaxJson(url = '', data, method = 'GET') {
		return new Promise((resolve, reject) => {
			uni.request({
				url,
				method,
				data,
				success: (res) => {
					resolve(res)
				},
				fail: (err) => {
					uni.showToast({
						title: '服务端错误，联系管理员!!!',
						icon: 'none',
					})
					reject(err)
				},
			})
		})
	}
}

export default new Request()
